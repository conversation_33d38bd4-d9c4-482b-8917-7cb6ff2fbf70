/* style.css */

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #fff;
    color: #333;
    line-height: 1.8;
    font-size: 18px;
}


html {
    overflow-y: scroll; /* Always show vertical scrollbar */
}

.lilac-heading {
    color: #C8A2C8;
}


header {
    /* Two layered backgrounds: shield on top, tartan behind */
    background-image: url('/images/bannerShield2.png'), url('/images/bannerBackground.PNG');
    background-repeat: no-repeat, repeat;
    /* Position the shield 20px from the left & top,
       while the tartan repeats from the top-left. */
    background-position: 20px 20px, left top;
    /* Scale the shield to 100px wide,
       the tartan tiles to 50px wide. */
    background-size: 100px auto, 150px auto;
    /* Banner padding (height) */
    padding: 40px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Move and resize the heading */
    header h1 {
        /* Slightly bigger heading */
        font-size: 3.3em;
        font-weight: bold;
        /* Nudge it so it starts after the shield area */
        margin-left: 100px;
        line-height: 1.2;
        text-shadow: 5px 5px 10px #0057B7;
    }


/* Lilac box for the login link in the banner */
.login-box {
    background-color: #0057B7; /* lilac */
    color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
}

    .login-box:hover {
        background-color: #b37cb3; /* a darker lilac on hover */
    }

header {
    /* existing properties */
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    /* etc. */
}

/* This container holds both buttons side by side */
.header-links {
    display: flex;
    /* A small gap for the space between them (adjust as needed) */
    gap: 10px;
}



.main-nav {
    background-color: #001B44; /* a deep navy, or #003F8F, etc. */
}

    .main-nav ul {
        display: flex;
        justify-content: center;
        align-items: center;
        list-style: none;
    }

        .main-nav ul li {
            position: relative;
            border-left: 1px solid #ADD8E6;
        }
            .main-nav ul li:first-child {
                border-left: none;
            }

            .main-nav ul li a {
                color: #fff;
                text-decoration: none;
                padding: 14px 20px;
                display: block;
                font-size: 1.1em;
            }

                .main-nav ul li a:hover {
                    background-color: #003F8F; /* highlight on hover */
                }

/* Custom pages dropdown styles */
.custom-pages-dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #001B44;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid #003F8F;
}

.dropdown-content a:hover {
    background-color: #003F8F;
}

.custom-pages-dropdown:hover .dropdown-content {
    display: block;
}






/**********************************************
  LANDING PAGE ANIMATIONS
**********************************************/

.rolling-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    border: 3px solid #C8A2C8; /* Lilac outline */
    background-color: #f9f9f9; /* If you want a background */
    height: 2.5em; /* Enough height for the text */
    display: flex;
    align-items: center; /* vertically center the text */
    margin-bottom: 10px;
}

.rolling-content {
    white-space: nowrap;
    display: inline-block;
    padding-left: 100%;
    animation: scrollBanner 15s linear infinite;
    font-weight: bold;
    color: #0057B7;
}

@keyframes scrollBanner {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-100%);
    }
}

/* Main Content */
main {
    position: relative;
    padding: 20px 20px;
    min-height: 80vh;
}


.landing-container {
    position: relative;
    min-height: 80vh;
    text-align: center;
    margin-top: 7px;
}

.landing-img {
    position: absolute;
    opacity: 0;
    transform-origin: center center;
    z-index: 1;
}


.mission-row {
    display: flex; /* side-by-side columns */
    justify-content: center; /* or space-between, etc. */
    align-items: flex-start; /* top alignment, for instance */
    gap: 1rem; /* space between columns */
    max-width: 1200px;
    margin: 0 auto; /* center the row horizontally */
    padding: 40px; /* optional padding around the row */
}

.content-flex-wrapper {
    display: flex;
    position: relative;
}


.left-col {
    flex: 0 0 23%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
}


.main-shield {
    max-width: 250px;
    width: 100%;
    margin-bottom: 0.5rem;
}

.main-ribbons {
    max-width: 200px;
    width: 100%;
    margin-top: 257px;
    margin-left: 0.42rem; /* shift horizontally if needed */
}

/* Adjust top positions & animation delays as needed */
#imageShield {
    opacity: 0;
    position: absolute;
    top: 2%;
    left: 2%;
    animation: growLeft 2s forwards ease-in-out;
    animation-delay: 1s; /* or 1s if you want it later */
    z-index: 2;
}

#imageBanner {
    opacity: 0;
    position: absolute;
    top: 97%;
    left: 2%;
    animation: growLeft 2s forwards ease-in-out;
    animation-delay: 1.5s; /* ribbons appear last */
    z-index: 2;
}

@keyframes growLeft {
    0% {
        opacity: 0;
        transform: translateX(-37%) scale(0.5);
    }

    100% {
        opacity: 1;
        transform: translateX(-37%) scale(0.9);
    }
}

.about-page #imageShield {
    left: 73% !important;
}

.about-page #imageBanner {
    left: 73% !important;
}

.contact-page #imageShield {
    left: 73% !important;
}

.contact-page #imageBanner {
    left: 73% !important;
}





/* Right col: heading + about-us + photo */
.right-col {
    width: 62vw;
    margin-left: auto; /* push it to the right half of the viewport */
    text-align: left; /* paragraphs left-aligned within this half */
    position: relative; /* optional if you want to position child elements absolutely */
}

/* about-us-landing fully visible from the start (remove opacity: 0) */
.about-us-landing {
    margin: 2rem auto; /* spacing around the block */
    max-width: 900px; /* or 70vw, etc. to limit line length */
    color: #0057B7;
    text-align: left; /* keep text left-aligned */
    line-height: 1.5; /* some comfortable line spacing */
}


/* Float the tutor0.jpg so text flows around/under it */
.float-right {
    float: right; /* anchor to the right side */
    margin-left: 1rem; /* gap between image & text */
    margin-bottom: 1rem; /* space below image so text doesn't butt up */
    max-width: 227px; /* or 40%, etc. to control image size */
    height: auto;
    border: 2px solid #C8A2C8; /* keep your lilac border if desired */
}

/* A new keyframes for the heading's fade & grow */
@keyframes fadeGrow {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }

    100% {
        opacity: 1;
        transform: scale(1.7);
    }
}

/* Apply it to .mission-statement */
.mission-statement {
    /* Hide initially & scale down a bit */
    opacity: 0;
    transform: scale(1.5);
    /* Animate in over 1.5s, appear fully at the end */
    animation: fadeGrow 1.5s forwards ease-in-out;
    /* Start slightly sooner so it appears before the shield */
    animation-delay: 0.3s;
    text-align: center;
    margin: 0.7rem auto 1rem auto; /* some spacing */
    color: #C8A2C8;
    padding: 1rem;
    margin-bottom: 1rem;
    margin-top: -20px; /* Raise the h2 slightly */
    margin-bottom: 30px; /* Add space between h2 and content below */
}

    .mission-statement h2 {
        font-size: 5.7em;
        font-weight: bold;
        margin-bottom: 1rem;
    }

/* Increase font size for the CTA paragraph */
.cta-text {
    font-size: 1.1em; /* or 1.2em, or 20px, etc. */
    line-height: 1.6; /* optional: a bit more spacing for readability */
}

.large-study-img {
    max-width: 60%;
    height: auto;
    border: 2px solid #C8A2C8;
    margin: 20px; /* Add margin around the image */
    margin-top: 40px; /* Push down the image slightly */
}

/* A quick fade/slide up (0.8s) with a short delay so it starts
   once the heading has basically appeared. */
@keyframes fadeUpQuick {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Initially hidden (like your fade-in-on-scroll approach, but simpler). */
.fade-later {
    opacity: 0;
    transform: translateY(20px);
    /* The keyframes below will override these once triggered. */
}

    /* We'll use forwards to retain final state,
   and set a short 0.8s duration + 1.5s or so delay
   (so it starts after the heading's done). */
    .fade-later.fade-in {
        animation: fadeUpQuick 0.5s 1.2s forwards ease-in-out;
    }



/* By default, .fade-in-on-scroll elements start hidden.
   We'll remove these inline styles once they're observed. */
.fade-in-on-scroll {
    opacity: 0;
    transform: translateY(20px);
}


.parents-zone-section {
    position: relative;
    background-color: #E6F0FF; /* a very light blue */
    margin-top: 127px;
    padding: 60px 0;
    margin-bottom: 207px;
    /* Put the background image behind a tinted overlay (option A: linear-gradient) */
    background: linear-gradient( rgba(255,255,255,0.7), /* White overlay at 70% opacity */
    rgba(255,255,255,0.7) ), url('/images/tutor2.jpg') no-repeat center center;
    background-size: cover; /* Make sure it covers the entire parent zone */
}

.parents-box {
    background-color: #fff;
    max-width: 800px;
    margin: 0 auto;
    padding: 40px;
    box-shadow: 0 0 15px rgba(0, 0, 139, 0.3); /* darker blue shadow */
    text-align: center;
}

    .parents-box h2 {
        font-size: 2em;
        color: #0057B7;
        margin-bottom: 1rem;
    }

    .parents-box p {
        font-size: 1.2em;
        color: #0057B7;
        margin-bottom: 1rem;
    }

.parents-extra-sections {
    margin-top: 12em; /* or whatever spacing you like */
}



.small-tutor-img {
    max-width: 50%;
    height: auto;
    display: block;
    margin: 1rem auto;
    position: absolute;
    top: 15vh; /* slightly below the mission statement heading */
    right: 0;
    width: 17%;
    border: 2px solid #C8A2C8;
}


.tutor-zone-page h2 {
    color: #C8A2C8; /* Lilac */
}

.tutor-zone-section {
    position: relative;
    margin: 0;
    padding: 77px 0;
    background:
    /* first the tinted overlay + tutor1.jpg */
    linear-gradient(rgba(255,255,255,0.7), rgba(255,255,255,0.7)), url('/images/tutor1.jpg') no-repeat center center,
    /* then behind that, the tartan repeated if you like */
    url('/images/bannerBackground.PNG') repeat left top;
    background-size: cover, cover, 377px auto;
}

.tutor-box {
    background-color: #fff;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 0 15px rgba(200,162,200, 0.3);
    border: 2px solid #C8A2C8;
    text-align: center;
}

    .tutor-box h2 {
        font-size: 2em;
        color: #0057B7;
        margin-bottom: 1rem;
    }

    .tutor-box p {
        font-size: 1.2em;
        color: #0057B7;
        margin-bottom: 1rem;
    }

/* Extra Tutor Zone styles */
.tutorzone-container {
    max-width: 900px;
    margin: 2rem auto;
    padding: 1rem;
}

/* Dynamic page styles */
.dynamic-page .right-col {
    padding: 20px;
}

.dynamic-page .page-featured-image {
    max-width: 100%;
    height: auto;
    margin-bottom: 20px;
    border: 2px solid #C8A2C8;
}

.dynamic-page .page-content {
    line-height: 1.6;
    color: #0057B7;
}

.dynamic-page .loading-indicator {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.tutorzone-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
}

    .tutorzone-section h2 {
        color: #0057B7;
        font-size: 2em;
        margin-bottom: 1rem;
        text-align: center;
    }
/* Example calendar placeholder */
.calendar {
    margin: 1rem auto;
    max-width: 600px;
    padding: 1rem;
    border: 1px solid #C8A2C8;
    border-radius: 4px;
    background-color: #fff;
    text-align: center;
}

/* Tutor Highlight Banner styling */
.tutor-highlight-banner {
    margin: 2rem auto;
    padding: 1rem;
    background-color: #C8A2C8;
    color: #fff;
    text-align: center;
    border-radius: 4px;
}

/* Directory Link Container styling */
.directory-link-container {
    margin: 2rem auto;
    padding: 1.5rem;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 90%;
}

.directory-link-container h3 {
    color: #0057B7;
    margin-bottom: 0.5rem;
}

.directory-link-container p {
    margin-bottom: 1.5rem;
}

.btn-primary {
    background-color: #0057B7;
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 4px;
    display: inline-block;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #003d80;
}

.btn-large {
    font-size: 1.2em;
    padding: 15px 30px;
}




/*
   Example: styling for the parent/tutor login buttons
   so they're 3 bigger and match the tutor search style
*/
.zone-login-btn {
    background-color: #0057B7; /* same as your tutor search button */
    color: #fff;
    border: none;
    padding: 23px 33px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 2.3em;
    }



/* Tagline and Form */
.content-wrapper {
    left: 53vw;
    top: 17vh;
}


/* Tagline Appears at End of Leaf Growth */
@keyframes revealTagline {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tagline {
    position: absolute;
    left: 57vw; /* Move it toward the end of the leaf */
    top: 12vh; /* Adjust placement */
    font-size: 2em;
    color: #0057B7;
    font-weight: bold;
    opacity: 0;
    animation: revealTagline 1s forwards ease-in-out;
    animation-delay: 1.8s; /* Happens AFTER leaf growth */
}

.tagline .lilac {
    color: #C8A2C8; /* Lilac color */
    font-weight: bold;
}



.tagline h2 {
    font-size: 2em;
}



.form-container {
    position: absolute;
    left: 37%; /* Moves it more central */
    top: 59vh; /* Moves it further down */
    transform: translate(-50%, -50%) scale(1.5);
    border: 1px solid #ddd;
    padding: 10px;
    max-width: 250px;
    color: #333;
    background-color: #f9f9f9;
}

    /* Add Lilac Shadow on Hover */
    .form-container:hover {
        box-shadow: 0px 0px 10px #C8A2C8;
    }


    .form-container h3 {
        margin-bottom: 15px;
    }

    .form-container label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .form-container select,
    .form-container input {
        width: 100%;
        padding: 8px;
        margin-bottom: 15px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    .form-container button {
        background-color: #0057B7;
        color: #fff;
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        border-radius: 4px;
        font-size: 1em;
        transition: all 0.3s ease-in-out;
    }

        /* Add Lilac Shadow on Hover */
        .form-container button:hover {
            box-shadow: 0px 0px 10px #C8A2C8;
        }

        /* Change Background to Lilac When Pressed */
        .form-container button:active {
            background-color: #C8A2C8;
            color: #fff;
        }



/* Tutor Grid */
.tutor-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0px;
    padding: 20px;
    margin-left: 5vw;
    margin-top: 23vh; /* Push the grid further down */
    margin-left: 21vw; /* Shift the grid to the right */
    opacity: 0; /* Initially hidden */
    transition: opacity 1s ease-in-out;
    row-gap: 20px; /* Adds vertical space between the rows */
}

/* Tutor Cards */
.tutor-card {
    width: 100%;
    height: 100%;
    border: 1px solid #ddd;
    padding: 0px;
    border-radius: 0;
    background-color: rgba(255, 255, 255, 1);
    text-align: center;
    position: relative;
    overflow: hidden;
    opacity: 0; /* Initially hidden */
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    border-bottom: 2px solid #ddd; /* Light gray bottom border */
    margin-bottom: 10px; /* Add some spacing between rows */
}

    .tutor-card p.available-in.custom-style {
        display: block;
        max-width: 70%; /* Restricts the width to 70% of the card */
        margin: 0 auto; /* Centers the content horizontally */
        font-size: 0.9em; /* Reduces the font size for better fit */
        text-align: center; /* Center-aligns the text */
        white-space: normal; /* Allows text to wrap onto multiple lines */
        word-wrap: break-word; /* Ensures long words break to fit */
        line-height: 1.3; /* Adjusts the line spacing for readability */
        color: #555; /* Makes the color a bit softer */
        padding: 0;
    }

    .tutor-card img {
        max-width: 100%;
        display: block; /* block-level so it starts at top of card */
        margin-top: 0;
        height: 57vh;
        object-fit: cover;
        border-radius: 1px;
    }

    .tutor-card::before {
        content: "";
        background: url('/images/flag.PNG') no-repeat center center;
        opacity: 0.4; /* Increase opacity to show more blue */
        background-blend-mode: overlay; /* Blend mode to enhance blue visibility */
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: -1;
    }

.badge-item {
    list-style: none;
    font-weight: bold;
    margin-bottom: 5px;
}

.badge-tick {
    color: #800080;
    font-size: 1.2em;
    margin-left: 5px; /* Small spacing between the badge description and the tick */
}



/* Purple Pound Symbols */
.purple-pound {
    color: #800080;
    font-weight: bold;
}

/* Pricing Key */
.pricing-key {
    position: static; /* or remove this line altogether */
    margin: 20px 0; /* give it top/bottom spacing */
    background: #f9f9f9;
    padding: 10px;
    font-size: 1.2rem;
    color: #800080;
    text-align: right;
    opacity: 0; /* Initially hidden */
    transition: opacity 1s ease-in-out;
}

/* Show tutor cards and pricing key after animation */
.show {
    opacity: 1;
}

/* Center the tutor-profile section */
.tutor-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin: 0 auto;
    max-width: 600px;
    position: relative;
    top: 14vh;
    background-color: transparent;
}

    .tutor-profile h1 {
        font-size: 2em;
        margin-bottom: 5px;
        color: #0057B7;
        text-align: center;
    }

    .tutor-profile p {
        font-size: 1.2em;
        margin-bottom: 15px;
        color: #333;
        text-align: center;
    }

    .tutor-profile img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .tutor-profile ul {
        font-size: 1.1em;
        list-style: none;
        padding: 0;
        text-align: center;
    }

    .tutor-profile li {
        margin-bottom: 5px;
    }


/* About Text */
.about-content {
    position: absolute;
    left: 29%;
    top: 17vh;
    width: 40vw;
    color: #0057B7; /* Matches banner color */
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
    opacity: 0;
    animation: revealAboutText 1.5s forwards ease-in-out;
    animation-delay: 1.8s;
}

    .about-content h2 {
        margin-bottom: 2.3rem; /* or 2rem for a bigger gap */
    }



@keyframes revealAboutText {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.blog-entry {
    max-width: 600px;
    margin: 2rem auto;
    text-align: left;
    border: 1px solid #ccc;
    padding: 1rem;
    background-color: #fff;
}

.blog-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin-bottom: 1rem;
}

.blog-filter {
    max-width: 600px;
    margin: 2rem auto;
    text-align: center;
    border: 2px solid #C8A2C8; /* lilac border */
    padding: 1em;
    border-radius: 4px;
    background-color: #f9f9f9;
}

    .blog-filter select {
        font-size: 1em;
        padding: 0.5em;
        border: 1px solid #C8A2C8;
        border-radius: 4px;
        margin-right: 0.5em;
    }

    .blog-filter button {
        background-color: #0057B7;
        color: #fff;
        border: none;
        padding: 0.5em 1em;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1em;
    }

        .blog-filter button:hover {
            background-color: #003F8F;
        }


/* The new blog form container (lilac box) */
#newBlogSection {
    max-width: 500px;
    margin: 2rem auto;
    background-color: #C8A2C8; /* Lilac */
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(200, 162, 200, 0.3);
    color: #fff;
    text-align: center;
}

    #newBlogSection h2 {
        margin-bottom: 1.5rem;
        font-size: 1.8rem;
    }

    #newBlogSection form label {
        display: block;
        text-align: left;
        margin: 1rem 0 0.5rem 0;
        font-weight: bold;
        color: #fff;
    }

    #newBlogSection form input[type="text"],
    #newBlogSection form input[type="datetime-local"],
    #newBlogSection form textarea,
    #newBlogSection form input[type="file"] {
        width: 100%;
        padding: 10px;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 1em;
    }

    #newBlogSection form button {
        background-color: #0057B7;
        color: #fff;
        border: none;
        padding: 12px 20px;
        cursor: pointer;
        border-radius: 4px;
        font-size: 1em;
        transition: all 0.3s ease-in-out;
    }

        #newBlogSection form button:hover {
            box-shadow: 0 0 10px #C8A2C8;
        }

        #newBlogSection form button:active {
            background-color: #C8A2C8;
            color: #fff;
        }

    /* Make the Category select bigger & styled in lilac */
    #newBlogSection form select#categoryField {
        font-size: 1.2em; /* Larger font */
        padding: 12px; /* Extra padding for a bigger click area */
        border: 2px solid #C8A2C8; /* Lilac border to match your theme */
        border-radius: 4px; /* Nicely rounded corners */
        margin-bottom: 1rem; /* Some spacing below */
        /* You can optionally set a fixed width if you want it wide, e.g. width: 100%; */
    }




.contact-info {
    text-align: left;
    color: #0057B7;
    opacity: 0;
    animation: fadeIn 1s forwards;
    animation-delay: 2s; /* or 1s, etc. */
}

    .contact-info h2 {
        font-size: 2.5em;
        margin-bottom: 1rem;
    }

    .contact-info p {
        font-size: 1.2em;
        line-height: 1.4;
    }

.newsletter-section, .social-links {
    margin: 2rem auto;
    max-width: 700px;
    text-align: center;
}

    .newsletter-section h2 {
        margin-bottom: 0.5rem;
    }

.newsletter-button {
    display: inline-block;
    background-color: #0057B7; /* Same as your zone-login-btn color */
    color: #fff;
    padding: 12px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 1.1em;
    transition: background-color 0.3s ease;
}

    .newsletter-button:hover {
        background-color: #003F8F; /* Slightly darker on hover */
    }

.social-links a {
    color: #0057B7;
    font-weight: bold;
    text-decoration: none;
}

    .social-links a:hover {
        text-decoration: underline;
    }

/* Tutor Directory overrides */
.tutor-directory-page #imageShield,
.tutor-directory-page #imageBanner {
    position: absolute;
}

/* Move them to the right and closer together */
.tutor-directory-page #imageShield {
    top: 10% !important;
    left: 60% !important;
}

.tutor-directory-page #imageBanner {
    top: 12% !important;
    left: 60% !important;
}

.dyn-block {
    max-width: 900px;
    margin: 60px auto;
    text-align: center;
}

    .dyn-block img {
        width: 100%;
        max-width: 800px;
        height: auto;
        object-fit: contain;
        margin: 1rem auto;
        display: block;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

/* Comprehensive Responsive Design Media Queries */
@media screen and (max-width: 1200px) {
    /* Global Container Styles */
    .landing-container,
    .tutorzone-container,
    .content-flex-wrapper,
    .form-container {
        width: 95%;
        margin: 0 auto;
    }

    /* Global Image Styles */
    img {
        max-width: 100%;
        height: auto;
        position: relative !important;
    }

    /* Header Adjustments */
    header {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        min-height: 177px; /* Increased height to fit bannerShield2.png */
    }

        header h1 {
            font-size: 1.8em;
            margin: 0;
            padding: 5px;
            flex: 1;
            text-align: center; /* Center align in landscape by default */
        }

    .header-links {
        position: absolute;
        bottom: 10px;
        right: 10px;
        padding: 5px;
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .banner-login-link {
        display: inline-block;
        margin: 0 5px;
        padding: 5px 10px;
        white-space: nowrap;
    }

    /* Column Layouts */
    .mission-row,
    .content-flex-wrapper {
        flex-direction: column;
    }

    .left-col, .right-col {
        width: 100%;
    }

    /* Shield and Ribbon spacing */
    .main-shield {
        margin: 30px auto;
        clear: both;
    }

    .main-ribbons {
        margin: 30px auto;
        clear: both;
    }

    /* Form container spacing */
    .form-container {
        margin: 30px auto;
        clear: both;
        padding: 25px;
        background-color: rgba(255, 255, 255, 0.95);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
}

@media screen and (max-width: 1200px) and (orientation: portrait) {
    /* Header Refinements */


    header h1 {
        /* Let it occupy about 60% of the header's width,
       and allow line breaks if it's long. */
        flex: 0 0 60%;
        font-size: 1.8em;
        margin: 0; /* no large left margin needed in mobile mode */
        padding: 5px;
        text-align: right; /* if you'd like to push it to the right side */
        word-wrap: break-word; /* or word-break: break-all; if you want forced breaks */
        line-height: 1.3;
    }

    .header-links {
        max-width: 40%;
        position: absolute;
        bottom: 10px;
        right: 40px; /* Moved more to the left from 10px */
    }

    /* Compact Navigation */
    .main-nav {
        width: 100%;
        padding: 5px 0;
        margin: 0;
        height: auto;
        max-height: 77vh;
    }

        .main-nav ul {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            padding: 5px;
            margin: 0;
            width: 100%;
        }

        .main-nav li {
            margin: 2px 0;
            padding: 3px 5px;
            font-size: 0.9em;
        }

            /* Left aligned links */
            .main-nav li:nth-child(odd) {
                text-align: left;
                padding-left: 15px;
            }

            /* Right aligned links */
            .main-nav li:nth-child(even) {
                text-align: right;
                padding-right: 15px;
            }

    /* Rolling Banner - Closer to Nav */
    .rolling-banner {
        margin-top: 0;
        padding-top: 5px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Shield and Ribbon adjustments */
    .main-shield {
        width: 50%;
        margin: 40px auto 20px;
        display: block;
        clear: both;
    }

    .main-ribbons {
        width: 50%;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        margin: 20px auto 40px;
        clear: both;
    }

    /* Form container specific adjustments */
    .form-container {
        width: 90%;
        margin: 40px auto;
        padding: 25px;
        clear: both;
        position: relative;
        z-index: 1;
    }

        /* Spacing between elements */
        .form-container + h1,
        .form-container + h2,
        .form-container + h3,
        .form-container + p,
        h1 + .form-container,
        h2 + .form-container,
        h3 + .form-container,
        p + .form-container {
            margin-top: 40px;
        }

    /* Specific adjustments for tutor directory and parents pages */
    .tutor-directory-page .form-container,
    .parents-zone .form-container {
        margin: 40px auto;
        clear: both;
        background-color: rgba(255, 255, 255, 0.95);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    /* Remove any floating styles on restricted viewports */
    .float-right,
    .float-left {
        float: none !important;
        display: block;
        margin: 20px auto;
    }
}

@media screen and (max-width: 768px) {
    /* Typography */
    body {
        font-size: 16px;
    }

    /* Banner Areas */
    .rolling-banner {
        height: auto;
        padding: 10px 0;
    }

    /* Headings */
    .mission-statement,
    .lilac-heading {
        font-size: 20px;
        text-align: center;
    }

    /* Text Alignment */
    .cta-text,
    .facebook-link {
        text-align: center;
    }

    /* Social Links */
    .social-links {
        flex-direction: column;
        align-items: center;
    }

    /* Newsletter */
    .newsletter-section {
        padding: 20px 10px;
    }

    .newsletter-button {
        width: 100%;
        margin: 10px 0;
    }

    .dyn-block img {
        max-width: 100%;
        margin: 0.5rem auto;
    }
}


@media screen and (max-width: 600px) {
    .mission-statement {
        margin-top: -15px; /* Slightly less negative margin on smaller screens */
    }

    .large-study-img {
        margin: 15px; /* Slightly smaller margins on smaller screens */
        margin-top: 30px;
    }
}

/* Additional breakpoint for very narrow screens */
@media screen and (max-width: 360px) {
    header h1 {
        font-size: 1.1em;
    }

    .banner-login-link {
        padding: 2px 6px;
        font-size: 0.8em;
    }

    .main-nav li {
        font-size: 0.8em;
    }
}

/* Specific rule for h1 in portrait mode on restricted viewports */
body.portrait-restricted header h1 {
    text-align: right;
    font-size: 1.6em;
    margin: 0;
    padding: 5px 15px 5px 5px;
    position: absolute;
    left: calc(50% - 0.5rem);
    top: 20px;
    width: 60%;
    word-wrap: break-word;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    transform: translate(-57px, 2px);
}



/* Restricted viewport mode (portrait + narrow) */
@media screen and (max-width: 600px) and (orientation: portrait) {
    /* Tutor image adjustments for portrait restricted viewport */
    body.restricted-viewport .large-study-img {
        transform: scale(1.33); /* Make image 33% larger */
        margin-top: 60px; /* Move down */
        margin-left: auto;
        margin-right: auto;
        display: block; /* Ensures centering works */
        float: none; /* Override any float */
        max-width: 80%; /* Adjust base size for the 1.33 scale */
    }

    /* Parents page resource section adjustment for portrait */
    body.restricted-viewport .parents-resources {
        margin-top: -50px; /* Move resources up closer to find-a-tutor div */
        padding-top: 0;
    }
}

/* Restricted viewport mode (landscape + narrow) */
@media screen and (max-width: 1200px) and (orientation: landscape) {
    /* Parents page resource section adjustment for landscape */
    body.restricted-viewport .parents-resources {
        margin-top: -50px; /* Move resources up closer to find-a-tutor div */
        padding-top: 0;
    }

    /* Tutor Directory page adjustments for landscape */
    body.restricted-viewport .tutorDirectory-page .tutor-finder {
        margin-top: 40px; /* Move finder below the paragraph */
        position: relative;
        clear: both;
    }
}

/* Ensure desktop sizes maintain original positioning */
@media screen and (min-width: 1201px) {
    .large-study-img {
        /* Maintain original desktop positioning */
        transform: none;
        margin: 20px;
        float: right;
    }

    .parents-resources {
        /* Maintain original desktop positioning */
        margin-top: initial;
        padding-top: initial;
    }

    .tutorDirectory-page .tutor-finder {
        /* Maintain original desktop positioning */
        margin-top: initial;
    }
}

/* Only for narrow landscape mode + restricted viewport + on the tutorDirectory page */
@media screen and (max-width: 1200px) and (orientation: landscape) {
    body.restricted-viewport.tutor-directory-page .form-container {
        /* Override the absolute positioning */
        position: relative; /* or relative—anything except absolute */
        margin-top: 77px; /* space below the <p> */
        left: auto; /* remove the left offset */
        top: auto; /* remove the top offset */
        transform: none; /* cancel the translate(-50%, -50%) scale(...) */
        /* (You can also adjust width, if needed) */
        max-width: 95%;
    }
}


/* High-DPI Screen Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .main-shield,
    .main-ribbons,
    header img[src*="bannerShield2"] {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Body class when dynamic sections are present */
body.has-dynamic-sections {
    padding-bottom: 2rem; /* Add some bottom padding */
}

/* Dynamic Sections Container */
#dynamicSections {
    margin-top: 8rem; /* Increased top margin */
    padding: 3rem 2rem;
    border-top: 3px solid #C8A2C8; /* Lilac separator line */
    width: 100%;
    max-width: 1080px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    clear: both; /* Ensure it clears any floated elements */
    display: block;
    float: none; /* Prevent floating */
    background-color: #f9f9f9; /* Light background to separate from main content */
    z-index: 1; /* Ensure proper stacking */
}

/* Clear separator before dynamic sections */
.dynamic-sections-separator {
    clear: both; /* Clear any floated elements */
    width: 100%;
    height: 150px; /* Significantly increased space */
    display: block;
    margin: 0;
    padding: 0;
    position: relative;
    background: linear-gradient(to bottom, transparent, #f9f9f9 70%);
    border-bottom: 1px dashed #C8A2C8; /* Add a subtle dashed line */
}

/* Add a visual indicator before the dynamic sections */
#dynamicSections::before {
    content: ''; /* Text indicator */
    display: block;
    text-align: center;
    font-size: 1.1rem;
    color: #0057B7;
    opacity: 0.8;
    margin-bottom: 2rem;
    font-style: italic;
    font-weight: 500;
    padding-bottom: 1rem;
    border-bottom: 3px solid #C8A2C8; /* Thicker separator line */
}

.dyn-block {
    max-width: 900px;
    margin: 3rem auto;
    padding: 2rem;
    background-color: #fff;
    border-radius: 1rem;
    box-shadow: 0 2px 18px rgba(0,87,183,0.05);
    position: relative;
    z-index: 1;
    clear: both; /* Ensure it clears any floated elements */
    float: none; /* Prevent floating */
    display: block; /* Ensure block display */
    border: 1px solid rgba(200,162,200,0.3); /* Subtle lilac border */
    overflow: hidden; /* Contain floated children */
}

.dyn-block img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    display: block;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dyn-block h2 {
    color: #0057B7;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    text-align: center;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #C8A2C8;
}

.dyn-block p {
    line-height: 1.6;
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Responsive adjustments for dynamic sections */
@media (max-width: 768px) {
    #dynamicSections {
        padding: 2rem 1rem;
        margin-top: 6rem; /* Increased from 4rem */
        width: 95%; /* Slightly narrower on mobile */
    }

    .dynamic-sections-separator {
        height: 100px; /* Increased from 60px */
        margin-top: 2rem; /* Add some top margin */
    }

    .dyn-block {
        padding: 1.5rem;
        margin: 2.5rem auto; /* Increased from 2rem */
        width: 95%; /* Slightly narrower on mobile */
    }

    .dyn-block h2 {
        font-size: 1.5rem;
        padding-bottom: 0.75rem; /* Add some padding */
    }

    .dyn-block p {
        font-size: 1rem;
        line-height: 1.5; /* Improved readability */
    }

    /* Make the separator more visible on mobile */
    #dynamicSections::before {
        font-size: 1rem;
        padding: 0.5rem 0;
        margin-bottom: 1.5rem;
    }
}

/* Enhanced Navigation Styling */
.nav-item {
    position: relative;
    border-left: 1px solid #ADD8E6;
    transition: background-color 0.3s ease;
}

.nav-item:first-child {
    border-left: none;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    padding: 14px 20px;
    display: block;
    font-size: 1.1em;
    transition: background-color 0.3s ease;
}

.nav-link:hover {
    background-color: #003F8F;
}

.nav-dropdown {
    display: none;
    position: absolute;
    background-color: #001B44;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    top: 100%;
    left: 0;
    border-radius: 0 0 4px 4px;
}

.nav-dropdown a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    border-bottom: 1px solid #003F8F;
    transition: background-color 0.3s ease;
}

.nav-dropdown a:hover {
    background-color: #003F8F;
}

.custom-pages-dropdown:hover .nav-dropdown {
    display: block;
}

/* Dynamic Page Hero Section */
.dynamic-page {
    position: relative;
    min-height: 100vh;
}

.dynamic-page .hero-section {
    position: relative;
    min-height: 300px;
    background-image: url('/images/bannerBackground.PNG');
    background-repeat: repeat;
    padding: 40px 20px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dynamic-page .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
    max-width: 800px;
    margin: 0 auto;
}

.dynamic-page .hero-shield {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 100px;
    height: auto;
    z-index: 1;
    opacity: 0.9;
}

/* Enhanced Rolling Banner for Dynamic Pages */
.dynamic-page .rolling-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    border: 3px solid #C8A2C8;
    background-color: #f9f9f9;
    height: 2.5em;
    display: flex;
    align-items: center;
    margin: 10px 0;
    z-index: 10;
}

.dynamic-page .rolling-content {
    white-space: nowrap;
    display: inline-block;
    padding-left: 100%;
    animation: scrollBanner 15s linear infinite;
    font-weight: bold;
    color: #0057B7;
}

/* Dynamic Page Content Styling */
.dynamic-page .page-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.dynamic-page .page-content h1 {
    color: #0057B7;
    font-size: 2.5em;
    margin-bottom: 1em;
    text-align: center;
}

.dynamic-page .page-content h2 {
    color: #C8A2C8;
    font-size: 2em;
    margin: 1.5em 0 1em;
}

.dynamic-page .page-content p {
    margin-bottom: 1.5em;
    line-height: 1.8;
}

.dynamic-page .page-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin: 1em 0;
}

/* Responsive Design for Dynamic Pages */
@media screen and (max-width: 768px) {
    .dynamic-page .hero-section {
        min-height: 200px;
        padding: 20px;
    }

    .dynamic-page .hero-shield {
        width: 80px;
        top: 10px;
        left: 10px;
    }

    .dynamic-page .page-content {
        padding: 15px;
    }

    .dynamic-page .page-content h1 {
        font-size: 2em;
    }

    .dynamic-page .page-content h2 {
        font-size: 1.5em;
    }

    .nav-dropdown {
        position: static;
        box-shadow: none;
        border-radius: 0;
    }

    .custom-pages-dropdown:hover .nav-dropdown {
        display: none;
    }

    .custom-pages-dropdown.active .nav-dropdown {
        display: block;
    }
}